import React, { useState } from "react"
import dynamic from "next/dynamic"
import { fetchAPI } from "@/lib/api"

const Dashboard = dynamic(() => import("@/components/dashbaord"), {
  ssr: false,
})

const CharacterModalSystem = ({
  session,
  t,
  characters,
  showInitial,
  charModalId,
}) => {
  const [showCharactersModal, setShowCharactersModal] = useState(showInitial)
  const [showDashboard, setShowDashboard] = useState(false)
  const [selectedCharacter, setSelectedCharacter] = useState(null)
  const [viewedCharacters, setViewedCharacters] = useState(new Set())

  const handleCharacterClick = (character) => {
    setSelectedCharacter(character)
    setShowDashboard(true)
    setShowCharactersModal(false)
    setViewedCharacters((prev) => new Set(prev).add(character.id))
  }

  const handleDashboardClose = () => {
    setShowDashboard(false)
    setSelectedCharacter(null)
    setShowCharactersModal(true)
  }

  const handleMainModalClose = async () => {
    console.log(session)
    try {
      await fetchAPI(
        `/char-modal/modal/${charModalId}`,
        {},
        {
          method: "PUT",
          body: JSON.stringify({
            data: {
              show: false,
            },
          }),
          headers: {
            Authorization: `Bearer ${session.jwt}`,
            "Content-Type": "application/json",
          },
        }
      )
      setShowCharactersModal(false)
    } catch (error) {
      console.error("Error updating character modal status:", error)
      // Still close the modal even if the update fails
      setShowCharactersModal(false)
    }
  }

  if (!showCharactersModal && !showDashboard) return null

  return (
    <>
      {showCharactersModal && (
        <div
          className="characters-modal-overlay"
          onClick={handleMainModalClose}
        >
          <div
            className="characters-modal-content"
            onClick={(e) => e.stopPropagation()}
          >
            <button className="close-button" onClick={handleMainModalClose}>
              ×
            </button>
            <div className="modal-titles">
              <h2>
                {t("Faites connaissance avec les membres de votre équipe")}
              </h2>
              <p>
                {t(
                  "Vous allez interagir avec ces personnages. Cliquez sur les photos pour découvrir leurs profils."
                )}
              </p>
            </div>
            <div className="row-onboarding flex-row d-flex pt-3">
              {characters.map((character) => (
                <div className="column" key={character.id}>
                  <div
                    className={`icon-container ${
                      viewedCharacters.has(character.id) ? "viewed" : ""
                    }`}
                    onClick={() => handleCharacterClick(character)}
                  >
                    <img
                      src={
                        process.env.NEXT_PUBLIC_API_URL +
                        character.attributes.photo.data.attributes.url
                      }
                      alt={character.attributes.name}
                    />
                  </div>
                  <h4>{character.attributes.name}</h4>
                  <p>{character.attributes.role}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {showDashboard && selectedCharacter && (
        <div className="dashboard-modal-overlay">
          <div className="dashboard-container">
            <Dashboard
              t={t}
              radarData={selectedCharacter.attributes.stats.radarData}
              character={{ data: [selectedCharacter] }}
              skillData={selectedCharacter.attributes.stats.skillData}
              type="onboarding"
              onClose={handleDashboardClose}
            />
          </div>
        </div>
      )}
    </>
  )
}

export default CharacterModalSystem
